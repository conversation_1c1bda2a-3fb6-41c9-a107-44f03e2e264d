'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import UdineConversation from '@/components/conversation/udine-conversation';
import {
  Copy,
  QrCode,
  Users,
  LogOut,
  Loader2
} from 'lucide-react';

type ConnectionStatus = 'connecting' | 'waiting' | 'connected' | 'disconnected';
type SessionPhase = 'prepare' | 'express' | 'understand' | 'resolve' | 'heal';

export default function RoomPage() {
  const params = useParams();
  const router = useRouter();
  const roomId = params.id as string;
  
  // State management
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('connecting');
  const [sessionPhase, setSessionPhase] = useState<SessionPhase>('prepare');
  const [connectedUsers, setConnectedUsers] = useState(0);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [otherUserSpeaking, setOtherUserSpeaking] = useState(false);
  const [roomUrl, setRoomUrl] = useState('');

  useEffect(() => {
    // Set the room URL for sharing
    setRoomUrl(`${window.location.origin}/room/${roomId}`);
    
    // Simulate connection process
    setTimeout(() => {
      setConnectionStatus('waiting');
      setConnectedUsers(1);
    }, 1000);
  }, [roomId]);

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(roomUrl);
      // TODO: Show toast notification
      console.log('Link copied to clipboard');
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleLeaveRoom = () => {
    router.push('/');
  };



  const getStatusColor = (status: ConnectionStatus) => {
    switch (status) {
      case 'connecting': return 'bg-yellow-500';
      case 'waiting': return 'bg-blue-500';
      case 'connected': return 'bg-green-500';
      case 'disconnected': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status: ConnectionStatus) => {
    switch (status) {
      case 'connecting': return 'Connecting...';
      case 'waiting': return 'Waiting for other person to join...';
      case 'connected': return 'Connected';
      case 'disconnected': return 'Disconnected';
      default: return 'Unknown';
    }
  };

  const getPhaseText = (phase: SessionPhase) => {
    switch (phase) {
      case 'prepare': return 'Preparing';
      case 'express': return 'Expressing';
      case 'understand': return 'Understanding';
      case 'resolve': return 'Resolving';
      case 'heal': return 'Healing';
      default: return 'In Progress';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Conversation Room
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Room ID: {roomId}
            </p>
          </div>
          <Button 
            variant="outline" 
            onClick={handleLeaveRoom}
            className="text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Leave Room
          </Button>
        </div>

        {/* Connection Status */}
        <Card className="mb-8 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${getStatusColor(connectionStatus)}`} />
              <span className="font-medium text-gray-900 dark:text-white">
                {getStatusText(connectionStatus)}
              </span>
              {connectionStatus === 'connecting' && (
                <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {connectedUsers}/2 connected
              </span>
            </div>
          </div>
        </Card>

        {/* Waiting State - Show sharing options */}
        {connectionStatus === 'waiting' && (
          <Card className="mb-8 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Share this room with the other person
            </h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={roomUrl}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-sm"
                />
                <Button onClick={handleCopyLink} size="sm">
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Link
                </Button>
              </div>
              <div className="flex justify-center">
                <Button variant="outline" size="sm">
                  <QrCode className="h-4 w-4 mr-2" />
                  Show QR Code
                </Button>
              </div>
            </div>
          </Card>
        )}

        {/* Session Progress - Show when connected */}
        {connectionStatus === 'connected' && (
          <Card className="mb-8 p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Session Progress
              </h3>
              <Badge variant="secondary">
                {getPhaseText(sessionPhase)}
              </Badge>
            </div>
            <div className="mt-4 flex space-x-2">
              {(['prepare', 'express', 'understand', 'resolve', 'heal'] as SessionPhase[]).map((phase, index) => (
                <div
                  key={phase}
                  className={`flex-1 h-2 rounded-full ${
                    index <= (['prepare', 'express', 'understand', 'resolve', 'heal'] as SessionPhase[]).indexOf(sessionPhase)
                      ? 'bg-blue-500'
                      : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                />
              ))}
            </div>
          </Card>
        )}

        {/* Speaking Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card className={`p-6 transition-all ${isSpeaking ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20' : ''}`}>
            <div className="text-center">
              <div className={`w-16 h-16 rounded-full mx-auto mb-3 flex items-center justify-center ${
                isSpeaking ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
              }`}>
                <Users className="h-8 w-8" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white">You</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {isSpeaking ? 'Speaking...' : 'Listening'}
              </p>
            </div>
          </Card>

          <Card className={`p-6 transition-all ${otherUserSpeaking ? 'ring-2 ring-green-500 bg-green-50 dark:bg-green-900/20' : ''}`}>
            <div className="text-center">
              <div className={`w-16 h-16 rounded-full mx-auto mb-3 flex items-center justify-center ${
                otherUserSpeaking ? 'bg-green-500 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
              }`}>
                <Users className="h-8 w-8" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white">Other Person</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {connectedUsers < 2 ? 'Not connected' : otherUserSpeaking ? 'Speaking...' : 'Listening'}
              </p>
            </div>
          </Card>
        </div>

        {/* AI Conversation Component */}
        <UdineConversation
          roomId={roomId}
          onSessionPhaseChange={(phase) => setSessionPhase(phase)}
          onConnectionStatusChange={(connected) => {
            setConnectionStatus(connected ? 'connected' : 'waiting');
          }}
        />

        {/* Instructions */}
        {connectionStatus === 'waiting' && (
          <Card className="mt-8 p-6 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
              What happens next?
            </h3>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Share the room link with the other person</li>
              <li>• Once they join, Udine (your AI mediator) will begin the session</li>
              <li>• Follow Udine's guidance for a structured, productive conversation</li>
              <li>• The room will disappear when you both leave</li>
            </ul>
          </Card>
        )}
      </div>
    </div>
  );
}
