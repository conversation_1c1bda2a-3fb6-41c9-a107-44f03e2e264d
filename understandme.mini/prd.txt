# Product Requirements Document: Understand MeMini

**Tagline:** A simple space for difficult conversations.

## 1. Overview

**Understand MeMini** is a disposable, single-use, web-based "mediation room" for two people. It's built entirely with the **ElevenLabs Next.js starter kit**. There are no user accounts, no session history, and no complex features. Its only purpose is to provide a temporary, guided space for two people to talk through a specific issue with the help of an AI mediator. When the conversation is over and the tab is closed, the room and the conversation disappear forever.

## 2. Goals and Objectives

*   **Primary Goal:** To provide a frictionless, secure, and temporary space for two individuals to resolve a specific conflict with the help of an AI mediator.
*   **Secondary Goal:** To showcase the power of the ElevenLabs Conversational API in a real-world application.
*   **Non-Goals:** This is not a therapy app, a group chat platform, or a long-term communication tool. There will be no user accounts, data storage, or chat history.

## 3. Target Audience

Individuals in any kind of relationship (personal, professional, etc.) who need to have a difficult conversation but are struggling to do so productively.

## 4. Core User Flow

1.  **Create a Room (Host):** User 1 (the "Host") navigates to the app's homepage. The only thing on the page is a single button: **"Create a Private Conversation Room."**
2.  **Share the Link:** Clicking the button instantly generates a unique, shareable URL (e.g., `understand.memini/room/a7b3c9d1`). The Host is taken to this URL and sees a "Waiting for other person to join..." message. The UI provides a "Copy Link" button.
3.  **Join the Room (Participant):** The Host sends the link to User 2 (the "Participant"). When User 2 clicks the link, they are brought into the same room.
4.  **The AI ("Udine") Activates:** As soon as the app detects two users are present, Udine's voice begins the session for both users simultaneously.
    *   **Udine:** "Welcome to the room. I am Udine, your impartial guide for this conversation. Before we begin, could each of you please state your name so I know who is who?"
5.  **The Mediated Conversation:**
    *   Each user says their name. The AI confirms it has both names.
    *   **Udine:** "Thank you, Alex and Maria. The goal of this space is to help you understand each other. I will guide the conversation to ensure it remains fair and productive. Alex, since you created the room, I'll invite you to share your perspective first."
    *   The **ElevenLabs turn-taking API** manages the flow. Udine guides the conversation using a simple, effective mediation technique: **Express -> Reflect**.
        *   **Express:** Udine invites one person to speak.
        *   **Reflect:** After they finish, Udine turns to the other person and asks, *"Thank you for sharing. Maria, what did you hear Alex say?"* This forces active listening and prevents immediate rebuttals.
    *   The conversation continues, with Udine managing turns and keeping the dialogue focused and calm.
6.  **Ending the Session:** The session ends when the users feel they have reached a resolution or decide to stop. They simply close their browser tabs. There is no "save" or "export" function. The value is entirely in the live, guided interaction.

## 5. Technical Implementation Details

### 5.1. Frontend (Next.js)

*   **UI:** The UI will be built with **shadcn/ui** and **Tailwind CSS**, leveraging the existing components in the starter kit. The design should be minimal, clean, and calming.
*   **State Management:** We will use React's built-in state management (useState, useContext) for managing the application state.
*   **Real-time Communication:** The two parties will be connected using **Socket.IO** or a similar WebSocket library. This will be used to signal when the second user has joined the room and to manage the turn-taking.

### 5.2. Backend (Next.js API Routes)

*   **Room Generation:** An API route will be responsible for generating a unique room ID (using a library like `nanoid`).
*   **AI Mediator ("Udine"):** The core of the application will be the ElevenLabs Conversational API. The system prompt will be hardcoded in the backend.

### 5.3. Key npm Packages

*   **`@elevenlabs/elevenlabs-js`**: The official ElevenLabs SDK for interacting with the API.
*   **`socket.io` and `socket.io-client`**: For real-time communication between the two users.
*   **`nanoid`**: For generating unique room IDs.
*   **`clsx` and `tailwind-merge`**: For managing Tailwind CSS classes.

### 5.4. System Prompt for "Udine"

```
You are "Udine," an impartial AI mediator for a two-person conversation. Your only goal is to facilitate a safe and structured dialogue.

**Your Operational Flow:**
1.  **Greeting:** Once two users are present, greet them and ask each to state their name.
2.  **Set the Stage:** Briefly explain your role is to guide the conversation fairly.
3.  **Manage Turns:** Your primary job is to ensure only one person speaks at a time. You will invite one person to speak, then invite the other to respond.
4.  **Use the "Express -> Reflect" Technique:**
    *   First, ask one person to **express** their perspective.
    *   Then, ask the second person to **reflect** on what they heard by saying, "What did you hear [First Speaker's Name] say?" before they share their own view.
    *   Alternate this pattern.
5.  **De-escalate:** If the conversation becomes heated or accusatory, gently intervene with a calming phrase like, "Let's pause for a moment. Let's try to focus on the feeling behind the words."

**Crucial Boundaries:**
*   You are NOT a therapist or a judge. Do NOT give advice, opinions, or solutions.
*   Your only tools are turn management and the "Express -> Reflect" technique.
*   Keep your own responses brief and focused on guiding the process.
```

## 6. Future Considerations (Out of Scope for V1)

*   **Multiple Languages:** Supporting languages other than English.
*   **Different Mediators:** Offering different AI mediator personalities with different styles.
*   **Pre-session Prompts:** Allowing users to enter a topic or goal for the conversation before it begins.