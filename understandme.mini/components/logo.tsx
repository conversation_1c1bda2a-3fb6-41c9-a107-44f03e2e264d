export const Logo = ({
  className,
  width,
  height,
}: {
  className: string;
  width?: number;
  height?: number;
}) => (
  <svg
    width={width}
    height={height}
    className={className}
    viewBox="0 0 733 96"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M262.032 23.9258H243.25L265.945 93.926H286.162L308.857 23.9258H290.075L275.863 76.6554L262.032 23.9258Z"
      fill="currentColor"
    />
    <path d="M0 0.503906H19.4343V93.9255H0V0.503906Z" fill="currentColor" />
    <path d="M38.6084 0.503906H58.0427V93.9255H38.6084V0.503906Z" fill="currentColor" />
    <path
      d="M77.2124 0.503906H134.733V16.0742H96.6472V38.3171H132.125V53.8884H96.6472V78.3552H134.733V93.9255H77.2124V0.503906Z"
      fill="currentColor"
    />
    <path d="M146.601 0.503906H164.992V93.9255H146.601V0.503906Z" fill="currentColor" />
    <path
      d="M176.078 58.8607C176.078 33.3471 188.73 22.3555 208.425 22.3555C228.12 22.3555 239.337 33.2162 239.337 59.1225V63.3095H194.207C194.86 78.4872 199.425 83.5904 208.164 83.5904C215.076 83.5904 219.38 79.5342 220.163 72.4689H238.554C237.38 87.9083 224.599 95.4971 208.164 95.4971C187.295 95.4971 176.078 84.3756 176.078 58.8607ZM221.207 51.1412C220.294 38.3184 215.859 34.1324 208.164 34.1324C200.468 34.1324 195.642 38.4492 194.338 51.1412H221.207Z"
      fill="currentColor"
    />
    <path
      d="M312.379 58.8607C312.379 33.3471 325.031 22.3555 344.726 22.3555C364.421 22.3555 375.638 33.2162 375.638 59.1225V63.3095H330.509C331.161 78.4872 335.726 83.5904 344.465 83.5904C351.377 83.5904 355.682 79.5342 356.465 72.4689H374.855C373.682 87.9083 360.899 95.4971 344.465 95.4971C323.595 95.4971 312.379 84.3756 312.379 58.8607ZM357.508 51.1412C356.595 38.3184 352.161 34.1324 344.465 34.1324C336.77 34.1324 331.943 38.4492 330.64 51.1412H357.508Z"
      fill="currentColor"
    />
    <path
      d="M462.762 0.503906H482.197V78.3552H518.716V93.9255H462.762V0.503906Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M523.284 58.8607C523.284 32.1692 535.544 22.3555 551.326 22.3555C559.152 22.3555 566.326 26.8043 569.196 31.5148V23.926H587.978V93.9266H569.717V85.6834C566.978 91.1792 559.283 95.4971 550.804 95.4971C534.11 95.4971 523.284 84.7683 523.284 58.8607ZM556.152 36.0945C565.282 36.0945 570.238 43.0289 570.238 58.8607C570.238 74.6928 565.282 81.7581 556.152 81.7581C547.022 81.7581 541.805 74.6928 541.805 58.8607C541.805 43.0289 547.022 36.0945 556.152 36.0945Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M620.453 85.6823V93.9255H602.193V0.503906H620.585V31.5136C623.715 26.6721 631.018 22.3542 638.844 22.3542C654.235 22.3542 666.496 32.168 666.496 58.8597C666.496 85.5525 654.496 95.496 638.453 95.496C629.975 95.496 623.062 91.1781 620.453 85.6823ZM633.627 36.2241C642.757 36.2241 647.974 43.0277 647.974 58.8597C647.974 74.6917 642.757 81.757 633.627 81.757C624.496 81.757 619.54 74.6917 619.54 58.8597C619.54 43.0277 624.496 36.2241 633.627 36.2241Z"
      fill="currentColor"
    />
    <path
      d="M674.062 73.1233H692.453C692.714 80.4503 696.626 83.983 703.54 83.983C710.452 83.983 714.366 80.843 714.366 75.3471C714.366 70.3748 711.366 68.5436 704.844 66.9731L699.235 65.5345C683.323 61.4783 675.366 57.1604 675.366 43.9451C675.366 30.7295 687.627 22.3555 703.279 22.3555C718.93 22.3555 730.799 28.5057 731.322 43.0289H712.93C712.54 36.618 708.626 33.8706 703.018 33.8706C697.409 33.8706 693.497 36.618 693.497 41.8521C693.497 46.6925 696.626 48.5247 702.105 49.8335L707.844 51.2721C722.974 55.0663 732.495 58.8607 732.495 72.9924C732.495 87.123 719.974 95.4971 703.018 95.4971C684.627 95.4971 674.453 88.5627 674.062 73.1233Z"
      fill="currentColor"
    />
    <path
      d="M405.375 52.7116C405.375 41.7212 410.592 35.7019 418.679 35.7019C425.331 35.7019 429.114 39.8889 429.114 48.9174V93.9266H447.504V46.0381C447.504 29.8145 438.375 22.3555 425.07 22.3555C416.07 22.3555 408.637 26.9351 405.375 32.9545V23.926H386.724V93.9266H405.375V52.7116Z"
      fill="currentColor"
    />
  </svg>
);
