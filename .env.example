# Understand MeMini Environment Variables

# ElevenLabs Configuration (Required)
ELEVENLABS_API_KEY="your_elevenlabs_api_key_here"     # Required: Get from https://elevenlabs.io/app/settings/api-keys
ELEVENLABS_AGENT_ID="your_udine_agent_id_here"        # Required: Create Udine agent in ElevenLabs dashboard

# Application Configuration (Required)
NEXT_PUBLIC_SITE_URL="http://localhost:3000"          # Required: Base URL for room link generation

# WebRTC TURN Server Configuration (Optional - for production)
TURN_SERVER_URL=""                                     # Optional: TURN server URL for NAT traversal
TURN_SERVER_USERNAME=""                                # Optional: TURN server username
TURN_SERVER_PASSWORD=""                                # Optional: TURN server password

# Development Configuration
NODE_ENV="development"                                 # Environment: development, production

# Task Master API Keys (Optional - for development workflow)
ANTHROPIC_API_KEY="your_anthropic_api_key_here"       # Optional: Format: sk-ant-api03-...
PERPLEXITY_API_KEY="your_perplexity_api_key_here"     # Optional: Format: pplx-...
OPENAI_API_KEY="your_openai_api_key_here"             # Optional: Format: sk-proj-...
GOOGLE_API_KEY="your_google_api_key_here"             # Optional: For Google Gemini models
GITHUB_API_KEY="your_github_api_key_here"             # Optional: Format: ghp_... or github_pat_...