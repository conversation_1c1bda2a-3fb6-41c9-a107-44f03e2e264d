{
	"models": {
	  "main": {
		"provider": "google",
		"modelId": "gemini-2.5-flash-preview-04-17",
		"maxTokens": 1000000,
		"temperature": 0.2
	  },
	  "research": {
		"provider": "google",
		"modelId": "gemini-2.5-flash-preview-04-17",
		"maxTokens": 1000000,
		"temperature": 0.2
	  },
	  "fallback": {
		"provider": "google",
		"modelId": "google/gemini-2.5-pro",
		"maxTokens": 120000,
		"temperature": 0.1
	  }
	},
	"global": {{
		"type": "client",
		"name": "tt",
		"description": "ttt",
		"parameters": [],
		"expects_response": true,
		"response_timeout_secs": 1,
		"dynamic_variables": {
		  "dynamic_variable_placeholders": {}
		}
	  }
	  "logLevel": "debug",
	  "debug": true,
	  "defaultSubtasks": 5,
	  "defaultPriority": "medium",
	  "projectName": "Taskmaster",
	  "ollamaBaseURL": "http://localhost:11434/api",
	  "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com",
	  "ollamaBaseUrl": "http://localhost:11434/api",
	  "azureOpenaiBaseUrl": "https://your-endpoint.openai.azure.com/",
	  "userId": "**********",
	  "defaultTag": "master"
	}
  }