{"name": "elevenlabs-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@elevenlabs/elevenlabs-js": "^2.0.1", "@elevenlabs/react": "^0.1.5", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.0", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@t3-oss/env-nextjs": "^0.12.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@wavesurfer/react": "^1.0.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "iron-session": "^8.0.4", "lucide-react": "^0.475.0", "nanoid": "^5.1.5", "next": "15.3.3", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "sonner": "^2.0.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "wavesurfer.js": "^7.9.1", "zod": "^3.24.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "simple-peer": "^9.11.1", "qrcode": "^1.5.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.0-canary.56", "tailwindcss": "^4", "typescript": "^5", "@types/simple-peer": "^9.11.8", "@types/qrcode": "^1.5.5"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}